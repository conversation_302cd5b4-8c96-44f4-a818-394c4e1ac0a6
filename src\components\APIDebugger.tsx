'use client'

import { useState, useEffect } from 'react'

interface ApiData {
  products: any[]
  debts: any[]
  payments: any[]
  balances: any[]
}

export default function APIDebugger() {
  const [data, setData] = useState<ApiData>({
    products: [],
    debts: [],
    payments: [],
    balances: []
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchAllData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      console.log('🔍 APIDebugger: Fetching all API data...')
      
      const [productsRes, debtsRes, paymentsRes, balancesRes] = await Promise.all([
        fetch('/api/products'),
        fetch('/api/debts'),
        fetch('/api/payments'),
        fetch('/api/customer-balances')
      ])

      if (!productsRes.ok) throw new Error(`Products API error: ${productsRes.status}`)
      if (!debtsRes.ok) throw new Error(`Debts API error: ${debtsRes.status}`)
      if (!paymentsRes.ok) throw new Error(`Payments API error: ${paymentsRes.status}`)
      if (!balancesRes.ok) throw new Error(`Balances API error: ${balancesRes.status}`)

      const [productsData, debtsData, paymentsData, balancesData] = await Promise.all([
        productsRes.json(),
        debtsRes.json(),
        paymentsRes.json(),
        balancesRes.json()
      ])

      const apiData = {
        products: productsData.products || [],
        debts: debtsData.debts || [],
        payments: paymentsData.payments || [],
        balances: balancesData.balances || []
      }

      console.log('✅ APIDebugger: Data fetched successfully:', {
        products: apiData.products.length,
        debts: apiData.debts.length,
        payments: apiData.payments.length,
        balances: apiData.balances.length
      })

      setData(apiData)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      console.error('❌ APIDebugger: Error:', errorMessage)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAllData()
  }, [])

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
        🔧 API Data Debugger
      </h2>
      
      <div className="mb-4">
        <button
          onClick={fetchAllData}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400"
        >
          {loading ? 'Loading...' : 'Refresh Data'}
        </button>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <strong>Error:</strong> {error}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <h3 className="font-semibold text-green-800 dark:text-green-200">Products</h3>
          <p className="text-2xl font-bold text-green-600 dark:text-green-400">
            {data.products.length}
          </p>
          <p className="text-sm text-green-600 dark:text-green-400">
            Total products in database
          </p>
        </div>

        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h3 className="font-semibold text-blue-800 dark:text-blue-200">Debts</h3>
          <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {data.debts.length}
          </p>
          <p className="text-sm text-blue-600 dark:text-blue-400">
            Total debt records
          </p>
        </div>

        <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <h3 className="font-semibold text-purple-800 dark:text-purple-200">Payments</h3>
          <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
            {data.payments.length}
          </p>
          <p className="text-sm text-purple-600 dark:text-purple-400">
            Total payment records
          </p>
        </div>

        <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
          <h3 className="font-semibold text-orange-800 dark:text-orange-200">Balances</h3>
          <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
            {data.balances.length}
          </p>
          <p className="text-sm text-orange-600 dark:text-orange-400">
            Customer balance records
          </p>
        </div>
      </div>

      {data.products.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
            Sample Product Data:
          </h3>
          <pre className="bg-gray-100 dark:bg-gray-700 p-4 rounded text-sm overflow-x-auto">
            {JSON.stringify(data.products[0], null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
        <p>This debugger helps verify that the API endpoints are working correctly.</p>
        <p>If you see data here but not in API Graphing, there's a processing issue.</p>
      </div>
    </div>
  )
}
