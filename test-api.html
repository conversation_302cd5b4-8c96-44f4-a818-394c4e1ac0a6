<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Tindahan Store</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Tindahan Store API Test</h1>
    <p>Testing API endpoints to verify data connectivity for API Graphing & Visuals</p>
    
    <div>
        <button onclick="testAPI('/api/products')">Test Products API</button>
        <button onclick="testAPI('/api/debts')">Test Debts API</button>
        <button onclick="testAPI('/api/payments')">Test Payments API</button>
        <button onclick="testAPI('/api/customer-balances')">Test Customer Balances API</button>
        <button onclick="testAllAPIs()">Test All APIs</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        async function testAPI(endpoint) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result';
            
            try {
                resultDiv.innerHTML = `<h3>Testing ${endpoint}...</h3><p>Loading...</p>`;
                resultsDiv.appendChild(resultDiv);
                
                const response = await fetch(endpoint);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ ${endpoint} - SUCCESS</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Data Count:</strong> ${getDataCount(data)}</p>
                        <details>
                            <summary>View Raw Data</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ ${endpoint} - ERROR</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }
        
        function getDataCount(data) {
            if (data.products) return `${data.products.length} products`;
            if (data.debts) return `${data.debts.length} debts`;
            if (data.payments) return `${data.payments.length} payments`;
            if (data.balances) return `${data.balances.length} balances`;
            return 'Unknown data structure';
        }
        
        async function testAllAPIs() {
            clearResults();
            const endpoints = ['/api/products', '/api/debts', '/api/payments', '/api/customer-balances'];
            
            for (const endpoint of endpoints) {
                await testAPI(endpoint);
                await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between requests
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
